"""
Data preprocessing module for multi-agent intent detection framework.
Handles creation of different ratios of in-domain vs out-of-domain intents.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from typing import Tuple, List, Dict
import logging
import os
from datetime import datetime

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/data_preprocessing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """
    Data preprocessor for intent detection with out-of-domain ratio control.
    """
    
    def __init__(self, train_path: str, test_path: str):
        """
        Initialize the data preprocessor.
        
        Args:
            train_path: Path to training data TSV file
            test_path: Path to test data TSV file
        """
        self.train_path = train_path
        self.test_path = test_path
        self.train_df = None
        self.test_df = None
        self.all_labels = None
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        logger.info("DataPreprocessor initialized")
        logger.info(f"Training data path: {train_path}")
        logger.info(f"Test data path: {test_path}")
    
    def load_data(self) -> None:
        """Load training and test data from TSV files."""
        try:
            self.train_df = pd.read_csv(self.train_path, sep='\t')
            self.test_df = pd.read_csv(self.test_path, sep='\t')
            
            logger.info(f"Training data loaded: {self.train_df.shape}")
            logger.info(f"Test data loaded: {self.test_df.shape}")
            
            # Get all unique labels
            train_labels = set(self.train_df['label'].unique())
            test_labels = set(self.test_df['label'].unique())
            self.all_labels = sorted(list(train_labels.union(test_labels)))
            
            logger.info(f"Total unique labels: {len(self.all_labels)}")
            logger.info(f"Sample labels: {self.all_labels[:10]}")
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def create_ood_split(self, in_domain_ratio: float, random_state: int = 42) -> Tuple[List[str], List[str]]:
        """
        Create in-domain and out-of-domain label splits.
        
        Args:
            in_domain_ratio: Ratio of labels to be considered in-domain (0.25, 0.5, or 0.75)
            random_state: Random seed for reproducibility
            
        Returns:
            Tuple of (in_domain_labels, out_of_domain_labels)
        """
        np.random.seed(random_state)
        
        # Calculate number of in-domain labels
        num_in_domain = int(len(self.all_labels) * in_domain_ratio)
        
        # Randomly select in-domain labels
        shuffled_labels = np.random.permutation(self.all_labels)
        in_domain_labels = shuffled_labels[:num_in_domain].tolist()
        out_of_domain_labels = shuffled_labels[num_in_domain:].tolist()
        
        logger.info(f"In-domain ratio: {in_domain_ratio}")
        logger.info(f"In-domain labels count: {len(in_domain_labels)}")
        logger.info(f"Out-of-domain labels count: {len(out_of_domain_labels)}")
        logger.info(f"Sample in-domain labels: {in_domain_labels[:5]}")
        logger.info(f"Sample out-of-domain labels: {out_of_domain_labels[:5]}")
        
        return in_domain_labels, out_of_domain_labels
    
    def prepare_training_data(self, in_domain_labels: List[str]) -> pd.DataFrame:
        """
        Prepare training data using only in-domain labels.
        
        Args:
            in_domain_labels: List of labels to be considered in-domain
            
        Returns:
            Filtered training DataFrame
        """
        # Filter training data to only include in-domain labels
        train_filtered = self.train_df[self.train_df['label'].isin(in_domain_labels)].copy()
        
        logger.info(f"Original training samples: {len(self.train_df)}")
        logger.info(f"Filtered training samples (in-domain only): {len(train_filtered)}")
        
        return train_filtered
    
    def prepare_test_data(self, in_domain_labels: List[str], out_of_domain_labels: List[str], 
                         num_samples: int = 100) -> pd.DataFrame:
        """
        Prepare test data with both in-domain and out-of-domain samples.
        
        Args:
            in_domain_labels: List of in-domain labels
            out_of_domain_labels: List of out-of-domain labels
            num_samples: Number of test samples to select
            
        Returns:
            Test DataFrame with mixed in-domain and out-of-domain samples
        """
        # Separate in-domain and out-of-domain test samples
        test_in_domain = self.test_df[self.test_df['label'].isin(in_domain_labels)]
        test_out_of_domain = self.test_df[self.test_df['label'].isin(out_of_domain_labels)]
        
        logger.info(f"Available in-domain test samples: {len(test_in_domain)}")
        logger.info(f"Available out-of-domain test samples: {len(test_out_of_domain)}")
        
        # Calculate samples to take from each category
        in_domain_ratio = len(in_domain_labels) / len(self.all_labels)
        num_in_domain_samples = int(num_samples * in_domain_ratio)
        num_ood_samples = num_samples - num_in_domain_samples
        
        # Sample from each category
        if len(test_in_domain) >= num_in_domain_samples:
            sampled_in_domain = test_in_domain.sample(n=num_in_domain_samples, random_state=42)
        else:
            sampled_in_domain = test_in_domain
            logger.warning(f"Not enough in-domain test samples. Using all {len(test_in_domain)} available.")
        
        if len(test_out_of_domain) >= num_ood_samples:
            sampled_ood = test_out_of_domain.sample(n=num_ood_samples, random_state=42)
        else:
            sampled_ood = test_out_of_domain
            logger.warning(f"Not enough out-of-domain test samples. Using all {len(test_out_of_domain)} available.")
        
        # Combine and shuffle
        test_mixed = pd.concat([sampled_in_domain, sampled_ood], ignore_index=True)
        test_mixed = test_mixed.sample(frac=1, random_state=42).reset_index(drop=True)
        
        # Add domain indicator
        test_mixed['domain'] = test_mixed['label'].apply(
            lambda x: 'in_domain' if x in in_domain_labels else 'out_of_domain'
        )
        
        logger.info(f"Final test set size: {len(test_mixed)}")
        logger.info(f"In-domain test samples: {len(test_mixed[test_mixed['domain'] == 'in_domain'])}")
        logger.info(f"Out-of-domain test samples: {len(test_mixed[test_mixed['domain'] == 'out_of_domain'])}")
        
        return test_mixed
    
    def save_processed_data(self, train_data: pd.DataFrame, test_data: pd.DataFrame, 
                           in_domain_ratio: float, output_dir: str = "processed_data") -> Dict[str, str]:
        """
        Save processed data to files.
        
        Args:
            train_data: Processed training data
            test_data: Processed test data
            in_domain_ratio: The in-domain ratio used
            output_dir: Directory to save processed data
            
        Returns:
            Dictionary with file paths
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Create filenames with ratio information
        ratio_str = str(int(in_domain_ratio * 100))
        train_file = f"{output_dir}/train_in_domain_{ratio_str}pct.tsv"
        test_file = f"{output_dir}/test_mixed_{ratio_str}pct.tsv"
        
        # Save data
        train_data.to_csv(train_file, sep='\t', index=False)
        test_data.to_csv(test_file, sep='\t', index=False)
        
        logger.info(f"Training data saved to: {train_file}")
        logger.info(f"Test data saved to: {test_file}")
        
        return {
            'train_file': train_file,
            'test_file': test_file
        }
    
    def process_data(self, in_domain_ratio: float = 0.75, num_test_samples: int = 100) -> Dict[str, str]:
        """
        Complete data processing pipeline.
        
        Args:
            in_domain_ratio: Ratio of labels to be considered in-domain
            num_test_samples: Number of test samples to prepare
            
        Returns:
            Dictionary with processed file paths
        """
        logger.info("Starting data processing pipeline")
        logger.info(f"In-domain ratio: {in_domain_ratio}")
        logger.info(f"Test samples: {num_test_samples}")
        
        # Load data
        self.load_data()
        
        # Create domain splits
        in_domain_labels, out_of_domain_labels = self.create_ood_split(in_domain_ratio)
        
        # Prepare training and test data
        train_data = self.prepare_training_data(in_domain_labels)
        test_data = self.prepare_test_data(in_domain_labels, out_of_domain_labels, num_test_samples)
        
        # Save processed data
        file_paths = self.save_processed_data(train_data, test_data, in_domain_ratio)
        
        logger.info("Data processing pipeline completed successfully")
        
        return file_paths

if __name__ == "__main__":
    # Example usage
    preprocessor = DataPreprocessor("data/banking/train.tsv", "data/banking/test.tsv")
    
    # Process data with 75% in-domain ratio and 100 test samples
    file_paths = preprocessor.process_data(in_domain_ratio=0.75, num_test_samples=100)
    
    print("Processed files:")
    for key, path in file_paths.items():
        print(f"{key}: {path}")
