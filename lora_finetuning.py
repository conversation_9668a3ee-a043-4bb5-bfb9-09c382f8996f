"""
LoRA fine-tuning module for multi-agent intent detection framework.
Implements LoRA fine-tuning for Qwen2.5:7B using the banking training dataset.
"""

import os
import logging
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    TrainingArguments, 
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import numpy as np

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/lora_finetuning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LoRAFineTuner:
    """
    LoRA fine-tuning class for intent classification.
    """
    
    def __init__(self, model_name: str = "Qwen/Qwen2.5-7B-Instruct", gpu_id: int = 0):
        """
        Initialize the LoRA fine-tuner.
        
        Args:
            model_name: HuggingFace model name
            gpu_id: GPU ID to use
        """
        self.model_name = model_name
        self.gpu_id = gpu_id
        self.tokenizer = None
        self.model = None
        self.peft_model = None
        
        # Set HuggingFace China mirror
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        
        logger.info("LoRAFineTuner initialized")
        logger.info(f"Model: {model_name}")
        logger.info(f"GPU ID: {gpu_id}")
        logger.info(f"HF_ENDPOINT: {os.environ.get('HF_ENDPOINT', 'default')}")
    
    def load_model_and_tokenizer(self) -> None:
        """Load the base model and tokenizer."""
        try:
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                padding_side="right"
            )
            
            # Add pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info("Loading model...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            
            logger.info("Model and tokenizer loaded successfully!")
            logger.info(f"Model device: {self.model.device}")
            logger.info(f"Model dtype: {self.model.dtype}")
            
        except Exception as e:
            logger.error(f"Error loading model and tokenizer: {str(e)}")
            raise
    
    def setup_lora_config(self, r: int = 16, alpha: int = 32, dropout: float = 0.1) -> None:
        """
        Setup LoRA configuration and apply to model.
        
        Args:
            r: LoRA rank
            alpha: LoRA alpha parameter
            dropout: LoRA dropout rate
        """
        try:
            logger.info("Setting up LoRA configuration...")
            
            lora_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                r=r,
                lora_alpha=alpha,
                lora_dropout=dropout,
                target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
                bias="none"
            )
            
            logger.info(f"LoRA config: r={r}, alpha={alpha}, dropout={dropout}")
            logger.info(f"Target modules: {lora_config.target_modules}")
            
            # Apply LoRA to model
            self.peft_model = get_peft_model(self.model, lora_config)
            self.peft_model.print_trainable_parameters()
            
            logger.info("LoRA configuration applied successfully!")
            
        except Exception as e:
            logger.error(f"Error setting up LoRA: {str(e)}")
            raise
    
    def format_training_data(self, text: str, label: str, labels_list: List[str]) -> str:
        """
        Format training data into instruction-following format.
        
        Args:
            text: Input text
            label: True label
            labels_list: List of all possible labels
            
        Returns:
            Formatted training string
        """
        labels_str = ", ".join(labels_list)
        
        formatted_text = f"""<|im_start|>system
You are an expert intent classification assistant. Your task is to analyze the given text and classify it into one of the provided intent categories.
<|im_end|>
<|im_start|>user
Text to classify: "{text}"

Available intent categories: {labels_str}

Please analyze this text and provide:
1. The most appropriate intent label from the given categories
2. Your reasoning and analysis process
3. Confidence level

Format your response as:
Intent: [selected_intent]
Analysis: [your detailed reasoning]
Confidence: [high/medium/low]
<|im_end|>
<|im_start|>assistant
Intent: {label}
Analysis: Based on the text "{text}", this clearly indicates a {label} intent. The user's request aligns with this category based on the specific language and context provided.
Confidence: high
<|im_end|>"""
        
        return formatted_text
    
    def prepare_dataset(self, train_file: str, max_length: int = 1024) -> Dataset:
        """
        Prepare training dataset from TSV file.
        
        Args:
            train_file: Path to training TSV file
            max_length: Maximum sequence length
            
        Returns:
            Prepared dataset
        """
        try:
            logger.info(f"Loading training data from: {train_file}")
            
            # Load data
            df = pd.read_csv(train_file, sep='\t')
            logger.info(f"Loaded {len(df)} training samples")
            
            # Get unique labels
            labels_list = sorted(df['label'].unique().tolist())
            logger.info(f"Number of unique labels: {len(labels_list)}")
            logger.info(f"Sample labels: {labels_list[:10]}")
            
            # Format data
            formatted_texts = []
            for _, row in df.iterrows():
                formatted_text = self.format_training_data(row['text'], row['label'], labels_list)
                formatted_texts.append(formatted_text)
            
            # Log sample formatted text
            logger.info("SAMPLE FORMATTED TRAINING TEXT:")
            logger.info(formatted_texts[0])
            logger.info("=" * 80)
            
            # Tokenize
            logger.info("Tokenizing data...")
            tokenized_data = []

            for i, text in enumerate(formatted_texts):
                if i % 1000 == 0:
                    logger.info(f"Tokenizing sample {i}/{len(formatted_texts)}")

                tokens = self.tokenizer(
                    text,
                    truncation=True,
                    padding="max_length",
                    max_length=max_length,
                    return_tensors=None
                )

                # Add labels for language modeling
                tokens["labels"] = tokens["input_ids"].copy()
                tokenized_data.append(tokens)
            
            # Create dataset
            dataset = Dataset.from_list(tokenized_data)
            logger.info(f"Dataset created with {len(dataset)} samples")
            
            return dataset
            
        except Exception as e:
            logger.error(f"Error preparing dataset: {str(e)}")
            raise
    
    def train(self, train_dataset: Dataset, output_dir: str = "lora_checkpoints",
              num_epochs: int = 3, batch_size: int = 4, learning_rate: float = 2e-4) -> None:
        """
        Train the model with LoRA.
        
        Args:
            train_dataset: Training dataset
            output_dir: Output directory for checkpoints
            num_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate
        """
        try:
            logger.info("Starting LoRA fine-tuning...")
            logger.info(f"Training samples: {len(train_dataset)}")
            logger.info(f"Epochs: {num_epochs}")
            logger.info(f"Batch size: {batch_size}")
            logger.info(f"Learning rate: {learning_rate}")
            
            # Training arguments
            training_args = TrainingArguments(
                output_dir=output_dir,
                num_train_epochs=num_epochs,
                per_device_train_batch_size=batch_size,
                gradient_accumulation_steps=4,
                warmup_steps=100,
                learning_rate=learning_rate,
                fp16=True,
                logging_steps=10,
                save_steps=500,
                save_total_limit=3,
                remove_unused_columns=False,
                dataloader_pin_memory=False,
                report_to=None
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=self.tokenizer,
                mlm=False,
                pad_to_multiple_of=8
            )
            
            # Trainer
            trainer = Trainer(
                model=self.peft_model,
                args=training_args,
                train_dataset=train_dataset,
                data_collator=data_collator,
                tokenizer=self.tokenizer
            )
            
            # Train
            logger.info("Starting training...")
            trainer.train()
            
            # Save final model
            final_output_dir = f"{output_dir}/final"
            trainer.save_model(final_output_dir)
            self.tokenizer.save_pretrained(final_output_dir)
            
            logger.info(f"Training completed! Model saved to: {final_output_dir}")
            
        except Exception as e:
            logger.error(f"Error during training: {str(e)}")
            raise
    
    def save_training_info(self, train_file: str, output_dir: str = "lora_checkpoints") -> None:
        """
        Save training information and metadata.
        
        Args:
            train_file: Path to training file used
            output_dir: Output directory
        """
        info = {
            'model_name': self.model_name,
            'train_file': train_file,
            'timestamp': datetime.now().isoformat(),
            'gpu_id': self.gpu_id,
            'hf_endpoint': os.environ.get('HF_ENDPOINT', 'default')
        }
        
        info_file = f"{output_dir}/training_info.json"
        with open(info_file, 'w') as f:
            json.dump(info, f, indent=2)
        
        logger.info(f"Training info saved to: {info_file}")

def main():
    """Main function for LoRA fine-tuning."""
    # Initialize fine-tuner
    fine_tuner = LoRAFineTuner()
    
    # Load model and tokenizer
    fine_tuner.load_model_and_tokenizer()
    
    # Setup LoRA
    fine_tuner.setup_lora_config()
    
    # Prepare dataset
    train_dataset = fine_tuner.prepare_dataset("processed_data/train_in_domain_75pct.tsv")
    
    # Train
    fine_tuner.train(train_dataset, num_epochs=2, batch_size=2)
    
    # Save training info
    fine_tuner.save_training_info("processed_data/train_in_domain_75pct.tsv")
    
    logger.info("LoRA fine-tuning completed successfully!")

if __name__ == "__main__":
    main()
