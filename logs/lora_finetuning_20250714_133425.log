2025-07-14 13:34:25,104 - INFO - LoRAFineTuner initialized
2025-07-14 13:34:25,104 - INFO - Model: Qwen/Qwen2.5-7B-Instruct
2025-07-14 13:34:25,104 - INFO - GPU ID: 0
2025-07-14 13:34:25,104 - INFO - HF_ENDPOINT: https://hf-mirror.com
2025-07-14 13:34:25,104 - INFO - Loading tokenizer...
2025-07-14 13:34:27,203 - INFO - Loading model...
2025-07-14 13:34:36,387 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-07-14 13:34:42,856 - INFO - Model and tokenizer loaded successfully!
2025-07-14 13:34:42,857 - INFO - Model device: cuda:0
2025-07-14 13:34:42,857 - INFO - Model dtype: torch.float16
2025-07-14 13:34:42,857 - INFO - Setting up LoRA configuration...
2025-07-14 13:34:42,857 - INFO - LoRA config: r=16, alpha=32, dropout=0.1
2025-07-14 13:34:42,857 - INFO - Target modules: {'gate_proj', 'q_proj', 'v_proj', 'o_proj', 'down_proj', 'up_proj', 'k_proj'}
2025-07-14 13:34:43,723 - INFO - LoRA configuration applied successfully!
2025-07-14 13:34:43,724 - INFO - Loading training data from: processed_data/train_in_domain_75pct.tsv
2025-07-14 13:34:43,741 - INFO - Loaded 6743 training samples
2025-07-14 13:34:43,742 - INFO - Number of unique labels: 57
2025-07-14 13:34:43,742 - INFO - Sample labels: ['Refund_not_showing_up', 'apple_pay_or_google_pay', 'atm_support', 'automatic_top_up', 'balance_not_updated_after_bank_transfer', 'balance_not_updated_after_cheque_or_cash_deposit', 'beneficiary_not_allowed', 'cancel_transfer', 'card_about_to_expire', 'card_arrival']
2025-07-14 13:34:44,040 - INFO - SAMPLE FORMATTED TRAINING TEXT:
2025-07-14 13:34:44,040 - INFO - <|im_start|>system
You are an expert intent classification assistant. Your task is to analyze the given text and classify it into one of the provided intent categories.
<|im_end|>
<|im_start|>user
Text to classify: "I made a cash deposit to my account but i don't see it"

Available intent categories: Refund_not_showing_up, apple_pay_or_google_pay, atm_support, automatic_top_up, balance_not_updated_after_bank_transfer, balance_not_updated_after_cheque_or_cash_deposit, beneficiary_not_allowed, cancel_transfer, card_about_to_expire, card_arrival, card_delivery_estimate, card_not_working, card_payment_fee_charged, card_payment_not_recognised, card_payment_wrong_exchange_rate, card_swallowed, change_pin, contactless_not_working, country_support, declined_card_payment, declined_cash_withdrawal, declined_transfer, disposable_card_limits, edit_personal_details, exchange_rate, exchange_via_app, extra_charge_on_statement, failed_transfer, get_disposable_virtual_card, get_physical_card, getting_spare_card, getting_virtual_card, lost_or_stolen_card, lost_or_stolen_phone, order_physical_card, passcode_forgotten, pending_card_payment, pending_cash_withdrawal, pending_top_up, pending_transfer, pin_blocked, reverted_card_payment?, supported_cards_and_currencies, terminate_account, top_up_by_bank_transfer_charge, top_up_by_cash_or_cheque, top_up_reverted, topping_up_by_card, transfer_fee_charged, transfer_into_account, transfer_not_received_by_recipient, unable_to_verify_identity, verify_source_of_funds, visa_or_mastercard, why_verify_identity, wrong_amount_of_cash_received, wrong_exchange_rate_for_cash_withdrawal

Please analyze this text and provide:
1. The most appropriate intent label from the given categories
2. Your reasoning and analysis process
3. Confidence level

Format your response as:
Intent: [selected_intent]
Analysis: [your detailed reasoning]
Confidence: [high/medium/low]
<|im_end|>
<|im_start|>assistant
Intent: balance_not_updated_after_cheque_or_cash_deposit
Analysis: Based on the text "I made a cash deposit to my account but i don't see it", this clearly indicates a balance_not_updated_after_cheque_or_cash_deposit intent. The user's request aligns with this category based on the specific language and context provided.
Confidence: high
<|im_end|>
2025-07-14 13:34:44,040 - INFO - ================================================================================
2025-07-14 13:34:44,040 - INFO - Tokenizing data...
2025-07-14 13:34:44,040 - INFO - Tokenizing sample 0/6743
2025-07-14 13:34:45,314 - INFO - Tokenizing sample 1000/6743
2025-07-14 13:34:46,595 - INFO - Tokenizing sample 2000/6743
2025-07-14 13:34:47,870 - INFO - Tokenizing sample 3000/6743
2025-07-14 13:34:49,133 - INFO - Tokenizing sample 4000/6743
2025-07-14 13:34:50,411 - INFO - Tokenizing sample 5000/6743
2025-07-14 13:34:51,691 - INFO - Tokenizing sample 6000/6743
2025-07-14 13:34:54,332 - INFO - Dataset created with 6743 samples
2025-07-14 13:34:54,638 - INFO - Starting LoRA fine-tuning...
2025-07-14 13:34:54,638 - INFO - Training samples: 6743
2025-07-14 13:34:54,642 - INFO - Epochs: 2
2025-07-14 13:34:54,643 - INFO - Batch size: 2
2025-07-14 13:34:54,643 - INFO - Learning rate: 0.0002
2025-07-14 13:34:56,840 - INFO - Starting training...
2025-07-14 13:34:57,214 - ERROR - Error during training: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`labels` in this case) have excessive nesting (inputs type `list` where type `int` is expected).
