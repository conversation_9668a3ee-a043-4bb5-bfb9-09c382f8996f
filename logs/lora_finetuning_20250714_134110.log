2025-07-14 13:41:10,353 - INFO - LoRAFineTuner initialized
2025-07-14 13:41:10,353 - INFO - Model: Qwen/Qwen2.5-7B-Instruct
2025-07-14 13:41:10,353 - INFO - GPU ID: 0
2025-07-14 13:41:10,353 - INFO - HF_ENDPOINT: https://hf-mirror.com
2025-07-14 13:41:10,353 - INFO - Loading tokenizer...
2025-07-14 13:41:11,529 - INFO - Loading model...
2025-07-14 13:41:20,406 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-07-14 13:41:25,969 - INFO - Model and tokenizer loaded successfully!
2025-07-14 13:41:25,969 - INFO - Model device: cuda:0
2025-07-14 13:41:25,969 - INFO - Model dtype: torch.float16
2025-07-14 13:41:25,969 - INFO - Setting up LoRA configuration...
2025-07-14 13:41:25,969 - INFO - LoRA config: r=16, alpha=32, dropout=0.1
2025-07-14 13:41:25,969 - INFO - Target modules: {'q_proj', 'v_proj', 'gate_proj', 'down_proj', 'o_proj', 'k_proj', 'up_proj'}
2025-07-14 13:41:26,839 - INFO - LoRA configuration applied successfully!
2025-07-14 13:41:26,839 - INFO - Loading training data from: processed_data/train_in_domain_75pct.tsv
2025-07-14 13:41:26,854 - INFO - Loaded 6743 training samples
2025-07-14 13:41:26,855 - INFO - Number of unique labels: 57
2025-07-14 13:41:26,855 - INFO - Sample labels: ['Refund_not_showing_up', 'apple_pay_or_google_pay', 'atm_support', 'automatic_top_up', 'balance_not_updated_after_bank_transfer', 'balance_not_updated_after_cheque_or_cash_deposit', 'beneficiary_not_allowed', 'cancel_transfer', 'card_about_to_expire', 'card_arrival']
2025-07-14 13:41:27,155 - INFO - SAMPLE FORMATTED TRAINING TEXT:
2025-07-14 13:41:27,155 - INFO - <|im_start|>system
You are an expert intent classification assistant. Your task is to analyze the given text and classify it into one of the provided intent categories.
<|im_end|>
<|im_start|>user
Text to classify: "I made a cash deposit to my account but i don't see it"

Available intent categories: Refund_not_showing_up, apple_pay_or_google_pay, atm_support, automatic_top_up, balance_not_updated_after_bank_transfer, balance_not_updated_after_cheque_or_cash_deposit, beneficiary_not_allowed, cancel_transfer, card_about_to_expire, card_arrival, card_delivery_estimate, card_not_working, card_payment_fee_charged, card_payment_not_recognised, card_payment_wrong_exchange_rate, card_swallowed, change_pin, contactless_not_working, country_support, declined_card_payment, declined_cash_withdrawal, declined_transfer, disposable_card_limits, edit_personal_details, exchange_rate, exchange_via_app, extra_charge_on_statement, failed_transfer, get_disposable_virtual_card, get_physical_card, getting_spare_card, getting_virtual_card, lost_or_stolen_card, lost_or_stolen_phone, order_physical_card, passcode_forgotten, pending_card_payment, pending_cash_withdrawal, pending_top_up, pending_transfer, pin_blocked, reverted_card_payment?, supported_cards_and_currencies, terminate_account, top_up_by_bank_transfer_charge, top_up_by_cash_or_cheque, top_up_reverted, topping_up_by_card, transfer_fee_charged, transfer_into_account, transfer_not_received_by_recipient, unable_to_verify_identity, verify_source_of_funds, visa_or_mastercard, why_verify_identity, wrong_amount_of_cash_received, wrong_exchange_rate_for_cash_withdrawal

Please analyze this text and provide:
1. The most appropriate intent label from the given categories
2. Your reasoning and analysis process
3. Confidence level

Format your response as:
Intent: [selected_intent]
Analysis: [your detailed reasoning]
Confidence: [high/medium/low]
<|im_end|>
<|im_start|>assistant
Intent: balance_not_updated_after_cheque_or_cash_deposit
Analysis: Based on the text "I made a cash deposit to my account but i don't see it", this clearly indicates a balance_not_updated_after_cheque_or_cash_deposit intent. The user's request aligns with this category based on the specific language and context provided.
Confidence: high
<|im_end|>
2025-07-14 13:41:27,155 - INFO - ================================================================================
2025-07-14 13:41:27,155 - INFO - Tokenizing data...
2025-07-14 13:41:27,155 - INFO - Tokenizing sample 0/6743
2025-07-14 13:41:28,539 - INFO - Tokenizing sample 1000/6743
2025-07-14 13:41:29,964 - INFO - Tokenizing sample 2000/6743
2025-07-14 13:41:31,380 - INFO - Tokenizing sample 3000/6743
2025-07-14 13:41:32,758 - INFO - Tokenizing sample 4000/6743
2025-07-14 13:41:34,206 - INFO - Tokenizing sample 5000/6743
2025-07-14 13:41:35,621 - INFO - Tokenizing sample 6000/6743
2025-07-14 13:41:40,657 - INFO - Dataset created with 6743 samples
2025-07-14 13:41:41,242 - INFO - Starting LoRA fine-tuning...
2025-07-14 13:41:41,247 - INFO - Training samples: 6743
2025-07-14 13:41:41,248 - INFO - Epochs: 2
2025-07-14 13:41:41,248 - INFO - Batch size: 2
2025-07-14 13:41:41,248 - INFO - Learning rate: 0.0002
2025-07-14 13:41:41,458 - INFO - Starting training...
2025-07-14 13:41:43,628 - ERROR - Error during training: CUDA out of memory. Tried to allocate 148.00 MiB. GPU 0 has a total capacity of 23.54 GiB of which 100.25 MiB is free. Process 2385510 has 23.44 GiB memory in use. Of the allocated memory 23.06 GiB is allocated by PyTorch, and 113.22 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
