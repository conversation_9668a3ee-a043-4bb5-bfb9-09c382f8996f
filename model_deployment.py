"""
Model deployment module for multi-agent intent detection framework.
Handles deployment of Qwen2.5:7B model using vLLM on GPU0.
"""

import os
import logging
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from vllm import LLM, SamplingParams
import torch

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/model_deployment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelDeployment:
    """
    Model deployment class for Qwen2.5:7B using vLLM.
    """
    
    def __init__(self, model_name: str = "Qwen/Qwen2.5-7B-Instruct", gpu_id: int = 0):
        """
        Initialize the model deployment.
        
        Args:
            model_name: HuggingFace model name
            gpu_id: GPU ID to use (default: 0)
        """
        self.model_name = model_name
        self.gpu_id = gpu_id
        self.llm = None
        self.sampling_params = None
        
        # Set HuggingFace China mirror
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
        
        logger.info("ModelDeployment initialized")
        logger.info(f"Model: {model_name}")
        logger.info(f"GPU ID: {gpu_id}")
        logger.info(f"HF_ENDPOINT: {os.environ.get('HF_ENDPOINT', 'default')}")
    
    def setup_sampling_params(self, temperature: float = 0.2, top_p: float = 0.9, 
                             max_tokens: int = 512) -> None:
        """
        Setup sampling parameters for inference.
        
        Args:
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            max_tokens: Maximum tokens to generate
        """
        self.sampling_params = SamplingParams(
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
            stop=["</s>", "<|endoftext|>"]
        )
        
        logger.info("Sampling parameters configured:")
        logger.info(f"  Temperature: {temperature}")
        logger.info(f"  Top-p: {top_p}")
        logger.info(f"  Max tokens: {max_tokens}")
    
    def load_model(self, tensor_parallel_size: int = 1, gpu_memory_utilization: float = 0.8) -> None:
        """
        Load the model using vLLM.
        
        Args:
            tensor_parallel_size: Number of GPUs for tensor parallelism
            gpu_memory_utilization: GPU memory utilization ratio
        """
        try:
            logger.info("Loading model with vLLM...")
            logger.info(f"Tensor parallel size: {tensor_parallel_size}")
            logger.info(f"GPU memory utilization: {gpu_memory_utilization}")
            
            # Set CUDA device
            os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
            
            self.llm = LLM(
                model=self.model_name,
                tensor_parallel_size=tensor_parallel_size,
                gpu_memory_utilization=gpu_memory_utilization,
                trust_remote_code=True,
                dtype="half",  # Use half precision for memory efficiency
                max_model_len=4096
            )
            
            logger.info("Model loaded successfully!")
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def format_prompt(self, text: str, labels: List[str], task_description: str = "") -> str:
        """
        Format input text into a prompt for intent classification.
        
        Args:
            text: Input text to classify
            labels: List of possible intent labels
            task_description: Additional task description
            
        Returns:
            Formatted prompt string
        """
        labels_str = ", ".join(labels)
        
        prompt = f"""<|im_start|>system
You are an expert intent classification assistant. Your task is to analyze the given text and classify it into one of the provided intent categories. Provide your analysis with clear reasoning.

{task_description}
<|im_end|>
<|im_start|>user
Text to classify: "{text}"

Available intent categories: {labels_str}

Please analyze this text and provide:
1. The most appropriate intent label from the given categories
2. Your reasoning and analysis process
3. Confidence level and any alternative considerations

Format your response as:
Intent: [selected_intent]
Analysis: [your detailed reasoning]
Confidence: [high/medium/low]
<|im_end|>
<|im_start|>assistant
"""
        
        return prompt
    
    def generate_response(self, prompts: List[str]) -> List[str]:
        """
        Generate responses for a list of prompts.
        
        Args:
            prompts: List of formatted prompts
            
        Returns:
            List of generated responses
        """
        if self.llm is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        if self.sampling_params is None:
            self.setup_sampling_params()
        
        try:
            logger.info(f"Generating responses for {len(prompts)} prompts...")
            
            # Log all prompts and responses
            for i, prompt in enumerate(prompts):
                logger.info(f"PROMPT {i+1}:")
                logger.info(prompt)
                logger.info("-" * 80)
            
            outputs = self.llm.generate(prompts, self.sampling_params)
            
            responses = []
            for i, output in enumerate(outputs):
                response = output.outputs[0].text.strip()
                responses.append(response)
                
                # Log response
                logger.info(f"RESPONSE {i+1}:")
                logger.info(response)
                logger.info("=" * 80)
            
            return responses
            
        except Exception as e:
            logger.error(f"Error generating responses: {str(e)}")
            raise
    
    def classify_intent(self, text: str, labels: List[str], 
                       task_description: str = "") -> Dict[str, Any]:
        """
        Classify a single text input into intent categories.
        
        Args:
            text: Input text to classify
            labels: List of possible intent labels
            task_description: Additional task description
            
        Returns:
            Dictionary with classification results
        """
        prompt = self.format_prompt(text, labels, task_description)
        responses = self.generate_response([prompt])
        
        response = responses[0]
        
        # Parse the response to extract intent, analysis, and confidence
        result = {
            'text': text,
            'raw_response': response,
            'prompt': prompt,
            'timestamp': datetime.now().isoformat()
        }
        
        # Simple parsing - in practice, you might want more robust parsing
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('Intent:'):
                result['predicted_intent'] = line.replace('Intent:', '').strip()
            elif line.startswith('Analysis:'):
                result['analysis'] = line.replace('Analysis:', '').strip()
            elif line.startswith('Confidence:'):
                result['confidence'] = line.replace('Confidence:', '').strip()
        
        # Log the classification result
        logger.info(f"CLASSIFICATION RESULT:")
        logger.info(f"Text: {text}")
        logger.info(f"Predicted Intent: {result.get('predicted_intent', 'N/A')}")
        logger.info(f"Analysis: {result.get('analysis', 'N/A')}")
        logger.info(f"Confidence: {result.get('confidence', 'N/A')}")
        logger.info("=" * 80)
        
        return result
    
    def batch_classify(self, texts: List[str], labels: List[str], 
                      task_description: str = "") -> List[Dict[str, Any]]:
        """
        Classify multiple texts in batch.
        
        Args:
            texts: List of input texts to classify
            labels: List of possible intent labels
            task_description: Additional task description
            
        Returns:
            List of classification results
        """
        logger.info(f"Starting batch classification for {len(texts)} texts")
        
        prompts = [self.format_prompt(text, labels, task_description) for text in texts]
        responses = self.generate_response(prompts)
        
        results = []
        for i, (text, response, prompt) in enumerate(zip(texts, responses, prompts)):
            result = {
                'text': text,
                'raw_response': response,
                'prompt': prompt,
                'timestamp': datetime.now().isoformat(),
                'batch_index': i
            }
            
            # Parse the response
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('Intent:'):
                    result['predicted_intent'] = line.replace('Intent:', '').strip()
                elif line.startswith('Analysis:'):
                    result['analysis'] = line.replace('Analysis:', '').strip()
                elif line.startswith('Confidence:'):
                    result['confidence'] = line.replace('Confidence:', '').strip()
            
            results.append(result)
        
        logger.info(f"Batch classification completed for {len(results)} texts")
        return results
    
    def save_results(self, results: List[Dict[str, Any]], filename: str) -> None:
        """
        Save classification results to a JSON file.
        
        Args:
            results: List of classification results
            filename: Output filename
        """
        os.makedirs('results', exist_ok=True)
        filepath = f"results/{filename}"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Results saved to: {filepath}")

if __name__ == "__main__":
    # Example usage
    deployment = ModelDeployment()
    deployment.setup_sampling_params(temperature=0.2, top_p=0.9)
    deployment.load_model()
    
    # Test with a simple example
    test_text = "I want to check my account balance"
    test_labels = ["balance_inquiry", "transfer_money", "card_activation", "customer_support"]
    
    result = deployment.classify_intent(test_text, test_labels)
    print("Classification result:", result)
